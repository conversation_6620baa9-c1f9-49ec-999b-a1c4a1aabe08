"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Users, Award, TrendingUp, Heart } from "lucide-react"

export default function HomePage() {
  const [loginData, setLoginData] = useState({ email: "", password: "", role: "" })
  const [registerData, setRegisterData] = useState({
    name: "",
    email: "",
    password: "",
    role: "",
    center: "",
  })

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()

    // Simple demo authentication
    if (!loginData.email || !loginData.password || !loginData.role) {
      alert("Please fill in all fields")
      return
    }

    // Mock user data
    const user = {
      id: "1",
      name: loginData.email.split("@")[0].charAt(0).toUpperCase() + loginData.email.split("@")[0].slice(1),
      email: loginData.email,
      role: loginData.role,
      center: "Main Center",
    }

    localStorage.setItem("user", JSON.stringify(user))

    // Redirect based on role
    const dashboardRoutes = {
      admin: "/admin",
      teacher: "/teacher",
      student: "/student",
      parent: "/parent",
    }

    window.location.href = dashboardRoutes[loginData.role as keyof typeof dashboardRoutes] || "/student"
  }

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault()
    // Mock registration
    alert("Registration successful! Please login.")
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Heart className="h-8 w-8 text-red-500" />
            <h1 className="text-4xl font-bold text-gray-900">ImpactScore</h1>
          </div>
          <p className="text-xl text-gray-600 mb-8">NGO Student Growth Tracker - Gamified Learning Platform</p>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="text-center">
              <CardContent className="pt-6">
                <Users className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                <h3 className="font-semibold">Multi-Role Access</h3>
                <p className="text-sm text-gray-600">Admin, Teacher, Student, Parent</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <Award className="h-12 w-12 text-yellow-500 mx-auto mb-2" />
                <h3 className="font-semibold">Gamification</h3>
                <p className="text-sm text-gray-600">XP Points, Badges, Leaderboards</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-2" />
                <h3 className="font-semibold">Progress Tracking</h3>
                <p className="text-sm text-gray-600">Behavior, Academics, Attendance</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <Heart className="h-12 w-12 text-red-500 mx-auto mb-2" />
                <h3 className="font-semibold">Impact Analytics</h3>
                <p className="text-sm text-gray-600">Reports, Trends, Insights</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Authentication */}
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Welcome to ImpactScore</CardTitle>
              <CardDescription>Login or register to get started</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="login" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="login">Login</TabsTrigger>
                  <TabsTrigger value="register">Register</TabsTrigger>
                </TabsList>

                <TabsContent value="login">
                  <form onSubmit={handleLogin} className="space-y-4">
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={loginData.email}
                        onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        value={loginData.password}
                        onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="role">Role</Label>
                      <Select
                        value={loginData.role}
                        onValueChange={(value) => setLoginData({ ...loginData, role: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select your role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="teacher">Teacher/Volunteer</SelectItem>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="parent">Parent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button type="submit" className="w-full">
                      Login
                    </Button>
                  </form>

                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium mb-2">Demo Credentials:</p>
                    <div className="space-y-1 text-xs">
                      <p>
                        <Badge variant="outline">Admin</Badge> <EMAIL> / password
                      </p>
                      <p>
                        <Badge variant="outline">Teacher</Badge> <EMAIL> / password
                      </p>
                      <p>
                        <Badge variant="outline">Student</Badge> <EMAIL> / password
                      </p>
                      <p>
                        <Badge variant="outline">Parent</Badge> <EMAIL> / password
                      </p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="register">
                  <form onSubmit={handleRegister} className="space-y-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={registerData.name}
                        onChange={(e) => setRegisterData({ ...registerData, name: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="reg-email">Email</Label>
                      <Input
                        id="reg-email"
                        type="email"
                        value={registerData.email}
                        onChange={(e) => setRegisterData({ ...registerData, email: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="reg-password">Password</Label>
                      <Input
                        id="reg-password"
                        type="password"
                        value={registerData.password}
                        onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="reg-role">Role</Label>
                      <Select
                        value={registerData.role}
                        onValueChange={(value) => setRegisterData({ ...registerData, role: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select your role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="teacher">Teacher/Volunteer</SelectItem>
                          <SelectItem value="student">Student</SelectItem>
                          <SelectItem value="parent">Parent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="center">Center</Label>
                      <Select
                        value={registerData.center}
                        onValueChange={(value) => setRegisterData({ ...registerData, center: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select center" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="main">Main Center</SelectItem>
                          <SelectItem value="north">North Branch</SelectItem>
                          <SelectItem value="south">South Branch</SelectItem>
                          <SelectItem value="east">East Branch</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button type="submit" className="w-full">
                      Register
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
