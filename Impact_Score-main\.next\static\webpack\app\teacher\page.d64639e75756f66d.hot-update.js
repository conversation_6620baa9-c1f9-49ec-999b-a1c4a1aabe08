"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./app/teacher/page.tsx":
/*!******************************!*\
  !*** ./app/teacher/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockStudents = [\n    {\n        id: 1,\n        name: \"Priya Sharma\",\n        xp: 1250,\n        level: 5,\n        attendance: 95,\n        badges: [\n            \"Good Student\",\n            \"Perfect Attendance\",\n            \"Helper\"\n        ],\n        recentActivity: \"Completed math homework\",\n        goals: [\n            \"Improve handwriting\",\n            \"Learn multiplication tables\"\n        ]\n    },\n    {\n        id: 2,\n        name: \"Rahul Kumar\",\n        xp: 980,\n        level: 4,\n        attendance: 88,\n        badges: [\n            \"Team Player\",\n            \"Creative Thinker\"\n        ],\n        recentActivity: \"Helped classmate with reading\",\n        goals: [\n            \"Read 5 books this month\",\n            \"Improve attendance\"\n        ]\n    },\n    {\n        id: 3,\n        name: \"Anita Singh\",\n        xp: 1100,\n        level: 4,\n        attendance: 92,\n        badges: [\n            \"Quick Learner\",\n            \"Good Behavior\"\n        ],\n        recentActivity: \"Scored 90% in science test\",\n        goals: [\n            \"Master fractions\",\n            \"Join art club\"\n        ]\n    }\n];\nconst behaviorCategories = [\n    {\n        id: \"attendance\",\n        name: \"Attendance\",\n        xp: 10\n    },\n    {\n        id: \"homework\",\n        name: \"Homework Completion\",\n        xp: 25\n    },\n    {\n        id: \"behavior\",\n        name: \"Good Behavior\",\n        xp: 15\n    },\n    {\n        id: \"participation\",\n        name: \"Class Participation\",\n        xp: 20\n    },\n    {\n        id: \"hygiene\",\n        name: \"Personal Hygiene\",\n        xp: 10\n    },\n    {\n        id: \"helping\",\n        name: \"Helping Others\",\n        xp: 30\n    }\n];\nfunction TeacherDashboard() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [xpForm, setXpForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"\",\n        points: \"\",\n        note: \"\"\n    });\n    const [goalForm, setGoalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student: \"\",\n        goal: \"\",\n        deadline: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherDashboard.useEffect\": ()=>{\n            const userData = localStorage.getItem(\"user\");\n            if (userData) {\n                const parsedUser = JSON.parse(userData);\n                if (parsedUser.role !== \"teacher\") {\n                    alert(\"Access denied. Teacher role required.\");\n                    window.location.href = \"/\";\n                    return;\n                }\n                setUser(parsedUser);\n            } else {\n                window.location.href = \"/\";\n            }\n        }\n    }[\"TeacherDashboard.useEffect\"], []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"user\");\n        window.location.href = \"/\";\n    };\n    const handleAwardXP = (e)=>{\n        e.preventDefault();\n        alert(\"Awarded \".concat(xpForm.points, \" XP to student for \").concat(xpForm.category));\n        setXpForm({\n            category: \"\",\n            points: \"\",\n            note: \"\"\n        });\n    };\n    const handleSetGoal = (e)=>{\n        e.preventDefault();\n        alert(\"Goal set for student: \".concat(goalForm.goal));\n        setGoalForm({\n            student: \"\",\n            goal: \"\",\n            deadline: \"\"\n        });\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen gradient-bg-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-effect border-b border-border/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-secondary\",\n                                                children: \"Teacher Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-lg\",\n                                                children: [\n                                                    \"Welcome back, \",\n                                                    user.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        className: \"bg-primary/10 text-primary border-primary/20 px-4 py-2 text-sm font-medium\",\n                                        children: \"Teacher\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleLogout,\n                                        className: \"border-2 hover:bg-destructive hover:text-destructive-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Logout\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"My Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: mockStudents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"XP Awarded Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"450\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-accent/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-8 w-8 text-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"Avg Attendance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"92%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-8 w-8 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"Active Goals\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-accent/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-8 w-8 text-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                        defaultValue: \"students\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"students\",\n                                        children: \"My Students\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"evaluate\",\n                                        children: \"Behavior Evaluation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"goals\",\n                                        children: \"Set Goals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"badges\",\n                                        children: \"Award Badges\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"students\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                                    children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-lg\",\n                                                                    children: student.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        \"Level \",\n                                                                        student.level\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: [\n                                                                student.xp,\n                                                                \" XP • \",\n                                                                student.attendance,\n                                                                \"% attendance\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"Progress to Next Level\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                student.xp,\n                                                                                \"/1500 XP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_10__.Progress, {\n                                                                    value: student.xp / 1500 * 100,\n                                                                    className: \"h-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: \"Recent Activity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: student.recentActivity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: [\n                                                                        \"Badges (\",\n                                                                        student.badges.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: student.badges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: badge\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: \"Current Goals\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"text-sm text-gray-600 space-y-1\",\n                                                                    children: student.goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 241,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                goal\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, student.id, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"evaluate\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            children: \"Award XP Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: \"Recognize student achievements and behaviors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: handleAwardXP,\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"student-select\",\n                                                                        children: \"Select Student\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                        value: selectedStudent,\n                                                                        onValueChange: setSelectedStudent,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                    placeholder: \"Choose a student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                        value: student.id.toString(),\n                                                                                        children: student.name\n                                                                                    }, student.id, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                        lineNumber: 270,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"category\",\n                                                                        children: \"Behavior Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                        value: xpForm.category,\n                                                                        onValueChange: (value)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                category: value\n                                                                            }),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                    placeholder: \"Select category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 285,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                children: behaviorCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                        value: category.id,\n                                                                                        children: [\n                                                                                            category.name,\n                                                                                            \" (+\",\n                                                                                            category.xp,\n                                                                                            \" XP)\"\n                                                                                        ]\n                                                                                    }, category.id, true, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                        lineNumber: 289,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"points\",\n                                                                        children: \"XP Points\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"points\",\n                                                                        type: \"number\",\n                                                                        value: xpForm.points,\n                                                                        onChange: (e)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                points: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter XP amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"note\",\n                                                                        children: \"Note (Optional)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                        id: \"note\",\n                                                                        value: xpForm.note,\n                                                                        onChange: (e)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                note: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Add a motivational note...\",\n                                                                        rows: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"submit\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Award XP\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            children: \"Behavior Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: \"Quick reference for XP values\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: behaviorCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: category.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            category.xp,\n                                                                            \" XP\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, category.id, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"goals\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Set Personal Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Create personalized goals for students\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSetGoal,\n                                                className: \"space-y-4 max-w-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"goal-student\",\n                                                                children: \"Select Student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                value: goalForm.student,\n                                                                onValueChange: (value)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        student: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                            placeholder: \"Choose a student\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                        children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                value: student.id.toString(),\n                                                                                children: student.name\n                                                                            }, student.id, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"goal-text\",\n                                                                children: \"Goal Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                id: \"goal-text\",\n                                                                value: goalForm.goal,\n                                                                onChange: (e)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        goal: e.target.value\n                                                                    }),\n                                                                placeholder: \"Describe the goal...\",\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"deadline\",\n                                                                children: \"Target Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"deadline\",\n                                                                type: \"date\",\n                                                                value: goalForm.deadline,\n                                                                onChange: (e)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        deadline: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Set Goal\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"badges\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Award Special Badges\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Recognize exceptional achievements\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    {\n                                                        name: \"Perfect Attendance\",\n                                                        description: \"100% attendance for a month\",\n                                                        color: \"bg-green-100 text-green-800\"\n                                                    },\n                                                    {\n                                                        name: \"Academic Excellence\",\n                                                        description: \"Top performer in academics\",\n                                                        color: \"bg-blue-100 text-blue-800\"\n                                                    },\n                                                    {\n                                                        name: \"Team Leader\",\n                                                        description: \"Outstanding leadership skills\",\n                                                        color: \"bg-purple-100 text-purple-800\"\n                                                    },\n                                                    {\n                                                        name: \"Creative Genius\",\n                                                        description: \"Exceptional creativity\",\n                                                        color: \"bg-pink-100 text-pink-800\"\n                                                    },\n                                                    {\n                                                        name: \"Helper\",\n                                                        description: \"Always helps classmates\",\n                                                        color: \"bg-yellow-100 text-yellow-800\"\n                                                    },\n                                                    {\n                                                        name: \"Improvement Star\",\n                                                        description: \"Most improved student\",\n                                                        color: \"bg-orange-100 text-orange-800\"\n                                                    }\n                                                ].map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4 space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(badge.color),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    badge.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: badge.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"w-full bg-transparent\",\n                                                                children: \"Award Badge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherDashboard, \"gdBBXXB8Wm7SgUU6y6zzRr89uIY=\");\n_c = TeacherDashboard;\nvar _c;\n$RefreshReg$(_c, \"TeacherDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/teacher/page.tsx\n"));

/***/ })

});