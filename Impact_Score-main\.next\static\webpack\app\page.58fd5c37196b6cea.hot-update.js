"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./components/theme-toggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        role: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        password: \"\",\n        role: \"\",\n        center: \"\"\n    });\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        // Simple demo authentication\n        if (!loginData.email || !loginData.password || !loginData.role) {\n            alert(\"Please fill in all fields\");\n            return;\n        }\n        // Mock user data\n        const user = {\n            id: \"1\",\n            name: loginData.email.split(\"@\")[0].charAt(0).toUpperCase() + loginData.email.split(\"@\")[0].slice(1),\n            email: loginData.email,\n            role: loginData.role,\n            center: \"Main Center\"\n        };\n        localStorage.setItem(\"user\", JSON.stringify(user));\n        // Redirect based on role\n        const dashboardRoutes = {\n            admin: \"/admin\",\n            teacher: \"/teacher\",\n            student: \"/student\",\n            parent: \"/parent\"\n        };\n        window.location.href = dashboardRoutes[loginData.role] || \"/student\";\n    };\n    const handleRegister = (e)=>{\n        e.preventDefault();\n        // Mock registration\n        alert(\"Registration successful! Please login.\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen gradient-bg-hero animated-bg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_9__.ThemeToggle, {}, void 0, false, {\n                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 shimmer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-10 w-10 text-accent feature-icon pulse-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent smooth-transition hover:scale-105\",\n                                        children: \"Mentor's Mark\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl text-secondary/80 mb-4 font-medium\",\n                                children: \"NGO Student Growth Tracker\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-muted-foreground mb-12 max-w-2xl mx-auto\",\n                                children: \"Empowering education through gamified learning experiences and comprehensive progress tracking\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"text-center card-hover glass-effect border-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-8 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-12 w-12 text-primary feature-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-lg mb-2 text-secondary\",\n                                                    children: \"Multi-Role Access\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                    children: \"Seamless access for Admin, Teacher, Student, and Parent roles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"text-center card-hover glass-effect border-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-8 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-full bg-accent/10 w-fit mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-12 w-12 text-accent feature-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-lg mb-2 text-secondary\",\n                                                    children: \"Gamification\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                    children: \"Engaging XP Points, Achievement Badges, and Dynamic Leaderboards\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"text-center card-hover glass-effect border-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-8 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-12 w-12 text-primary feature-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-lg mb-2 text-secondary\",\n                                                    children: \"Progress Tracking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                    children: \"Comprehensive monitoring of Behavior, Academics, and Attendance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"text-center card-hover glass-effect border-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-8 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-full bg-accent/10 w-fit mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-12 w-12 text-accent feature-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-lg mb-2 text-secondary\",\n                                                    children: \"Impact Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                    children: \"Detailed Reports, Trend Analysis, and Actionable Insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-lg mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass-effect border-0 shadow-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"text-center pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-2xl font-bold text-secondary\",\n                                            children: \"Welcome to ImpactScore\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-muted-foreground text-base\",\n                                            children: \"Login or register to begin your educational journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                        defaultValue: \"login\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                                className: \"grid w-full grid-cols-2 bg-muted/50 p-1 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                        value: \"login\",\n                                                        className: \"data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium\",\n                                                        children: \"Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                        value: \"register\",\n                                                        className: \"data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium\",\n                                                        children: \"Register\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                value: \"login\",\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: handleLogin,\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"email\",\n                                                                        className: \"text-secondary font-medium\",\n                                                                        children: \"Email Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"email\",\n                                                                        type: \"email\",\n                                                                        placeholder: \"Enter your email\",\n                                                                        value: loginData.email,\n                                                                        onChange: (e)=>setLoginData({\n                                                                                ...loginData,\n                                                                                email: e.target.value\n                                                                            }),\n                                                                        className: \"h-12 border-2 border-border focus:border-primary transition-colors\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"password\",\n                                                                        className: \"text-secondary font-medium\",\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"password\",\n                                                                        type: \"password\",\n                                                                        placeholder: \"Enter your password\",\n                                                                        value: loginData.password,\n                                                                        onChange: (e)=>setLoginData({\n                                                                                ...loginData,\n                                                                                password: e.target.value\n                                                                            }),\n                                                                        className: \"h-12 border-2 border-border focus:border-primary transition-colors\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"role\",\n                                                                        className: \"text-secondary font-medium\",\n                                                                        children: \"Select Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                        value: loginData.role,\n                                                                        onValueChange: (value)=>setLoginData({\n                                                                                ...loginData,\n                                                                                role: value\n                                                                            }),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                className: \"h-12 border-2 border-border focus:border-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                    placeholder: \"Choose your role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 198,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 197,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"admin\",\n                                                                                        children: \"Admin\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 201,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"teacher\",\n                                                                                        children: \"Teacher/Volunteer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 202,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"student\",\n                                                                                        children: \"Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 203,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"parent\",\n                                                                                        children: \"Parent\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 204,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 200,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                type: \"submit\",\n                                                                className: \"w-full h-12 btn-primary-gradient text-lg font-semibold\",\n                                                                children: \"Sign In to ImpactScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 p-6 bg-muted/30 rounded-xl border border-border/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-semibold mb-4 text-secondary flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-accent rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Demo Credentials\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-3 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-primary/10 text-primary border-primary/20\",\n                                                                                children: \"Admin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 222,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-accent/10 text-accent border-accent/20\",\n                                                                                children: \"Teacher\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-primary/10 text-primary border-primary/20\",\n                                                                                children: \"Student\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 229,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 230,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"bg-accent/10 text-accent border-accent/20\",\n                                                                                children: \"Parent\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground mt-3 text-center\",\n                                                                children: [\n                                                                    \"Password: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-mono bg-background px-2 py-1 rounded\",\n                                                                        children: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                                value: \"register\",\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleRegister,\n                                                    className: \"space-y-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"name\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Full Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"name\",\n                                                                    placeholder: \"Enter your full name\",\n                                                                    value: registerData.name,\n                                                                    onChange: (e)=>setRegisterData({\n                                                                            ...registerData,\n                                                                            name: e.target.value\n                                                                        }),\n                                                                    className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"reg-email\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"reg-email\",\n                                                                    type: \"email\",\n                                                                    placeholder: \"Enter your email\",\n                                                                    value: registerData.email,\n                                                                    onChange: (e)=>setRegisterData({\n                                                                            ...registerData,\n                                                                            email: e.target.value\n                                                                        }),\n                                                                    className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"reg-password\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"reg-password\",\n                                                                    type: \"password\",\n                                                                    placeholder: \"Create a password\",\n                                                                    value: registerData.password,\n                                                                    onChange: (e)=>setRegisterData({\n                                                                            ...registerData,\n                                                                            password: e.target.value\n                                                                        }),\n                                                                    className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"reg-role\",\n                                                                            className: \"text-secondary font-medium\",\n                                                                            children: \"Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                            value: registerData.role,\n                                                                            onValueChange: (value)=>setRegisterData({\n                                                                                    ...registerData,\n                                                                                    role: value\n                                                                                }),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                    className: \"h-11 border-2 border-border focus:border-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                        placeholder: \"Select role\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 291,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"teacher\",\n                                                                                            children: \"Teacher/Volunteer\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 294,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"student\",\n                                                                                            children: \"Student\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 295,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"parent\",\n                                                                                            children: \"Parent\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 296,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"center\",\n                                                                            className: \"text-secondary font-medium\",\n                                                                            children: \"Center\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                            value: registerData.center,\n                                                                            onValueChange: (value)=>setRegisterData({\n                                                                                    ...registerData,\n                                                                                    center: value\n                                                                                }),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                    className: \"h-11 border-2 border-border focus:border-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                        placeholder: \"Select center\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"main\",\n                                                                                            children: \"Main Center\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 311,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"north\",\n                                                                                            children: \"North Branch\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 312,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"south\",\n                                                                                            children: \"South Branch\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 313,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                            value: \"east\",\n                                                                                            children: \"East Branch\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 314,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            className: \"w-full h-12 btn-accent-gradient text-lg font-semibold mt-6\",\n                                                            children: \"Create Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"qwi95Ci9fALINql8qdlP71/CyvU=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});