@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Custom color palette based on provided colors */
    /* #FDFBEE - Light cream background */
    --background: 48 75% 96%;
    --foreground: 180 100% 16%;

    /* Cards with subtle cream tint */
    --card: 48 60% 98%;
    --card-foreground: 180 100% 16%;

    /* Popovers */
    --popover: 48 60% 98%;
    --popover-foreground: 180 100% 16%;

    /* #57B4BA - Teal as primary */
    --primary: 184 36% 53%;
    --primary-foreground: 48 75% 96%;

    /* #015551 - Dark teal as secondary */
    --secondary: 178 98% 17%;
    --secondary-foreground: 48 75% 96%;

    /* Muted variations */
    --muted: 48 30% 92%;
    --muted-foreground: 180 20% 40%;

    /* #FE4F2D - Orange as accent */
    --accent: 11 99% 59%;
    --accent-foreground: 48 75% 96%;

    /* Destructive using orange tone */
    --destructive: 11 99% 59%;
    --destructive-foreground: 48 75% 96%;

    /* Borders and inputs */
    --border: 48 20% 85%;
    --input: 48 20% 90%;
    --ring: 184 36% 53%;
    --radius: 0.75rem;

    /* Chart colors using the palette */
    --chart-1: 184 36% 53%;
    --chart-2: 178 98% 17%;
    --chart-3: 11 99% 59%;
    --chart-4: 48 75% 70%;
    --chart-5: 184 50% 35%;
  }

  .dark {
    /* Dark mode using darker teal as base */
    --background: 180 100% 8%;
    --foreground: 48 75% 96%;

    /* Dark cards */
    --card: 180 80% 12%;
    --card-foreground: 48 75% 96%;

    /* Dark popovers */
    --popover: 180 80% 12%;
    --popover-foreground: 48 75% 96%;

    /* Teal primary in dark mode */
    --primary: 184 36% 53%;
    --primary-foreground: 180 100% 8%;

    /* Darker secondary */
    --secondary: 180 60% 20%;
    --secondary-foreground: 48 75% 96%;

    /* Dark muted */
    --muted: 180 40% 15%;
    --muted-foreground: 48 30% 70%;

    /* Orange accent in dark mode */
    --accent: 11 99% 59%;
    --accent-foreground: 180 100% 8%;

    /* Destructive in dark mode */
    --destructive: 11 80% 45%;
    --destructive-foreground: 48 75% 96%;

    /* Dark borders and inputs */
    --border: 180 40% 25%;
    --input: 180 40% 20%;
    --ring: 184 36% 53%;

    /* Dark chart colors */
    --chart-1: 184 50% 60%;
    --chart-2: 178 80% 30%;
    --chart-3: 11 90% 65%;
    --chart-4: 48 60% 50%;
    --chart-5: 184 40% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom gradient backgrounds and effects */
@layer components {
  .gradient-bg-primary {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(184 20% 90%) 50%, hsl(var(--background)) 100%);
  }

  .gradient-bg-hero {
    background: linear-gradient(135deg,
      hsl(48 75% 96%) 0%,
      hsl(184 30% 85%) 25%,
      hsl(184 36% 53%) 50%,
      hsl(178 98% 17%) 75%,
      hsl(180 100% 16%) 100%);
  }

  .card-hover {
    transition: all 0.3s ease;
    border: 1px solid hsl(var(--border));
  }

  .card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: hsl(var(--primary));
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(253, 251, 238, 0.8);
    border: 1px solid rgba(87, 180, 186, 0.2);
  }

  .dark .glass-effect {
    background: rgba(1, 85, 81, 0.8);
    border: 1px solid rgba(87, 180, 186, 0.3);
  }

  .feature-icon {
    transition: all 0.3s ease;
  }

  .feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
  }

  .btn-primary-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(178 98% 17%) 100%);
    border: none;
    color: hsl(var(--primary-foreground));
    transition: all 0.3s ease;
  }

  .btn-primary-gradient:hover {
    background: linear-gradient(135deg, hsl(178 98% 17%) 0%, hsl(var(--primary)) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(87, 180, 186, 0.3);
  }

  .btn-accent-gradient {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(11 80% 45%) 100%);
    border: none;
    color: hsl(var(--accent-foreground));
    transition: all 0.3s ease;
  }

  .btn-accent-gradient:hover {
    background: linear-gradient(135deg, hsl(11 80% 45%) 0%, hsl(var(--accent)) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(254, 79, 45, 0.3);
  }

  /* Animated background patterns */
  .animated-bg {
    background-image:
      radial-gradient(circle at 20% 80%, rgba(87, 180, 186, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(1, 85, 81, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(254, 79, 45, 0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse animation for icons */
  .pulse-icon {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  /* Shimmer effect */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
