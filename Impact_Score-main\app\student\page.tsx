"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Award, Star, TrendingUp, Calendar, LogOut, Trophy, Flame } from "lucide-react"

// Mock student data
const studentData = {
  name: "<PERSON><PERSON> <PERSON>",
  level: 5,
  xp: 1250,
  nextLevelXP: 1500,
  totalBadges: 8,
  streak: 12,
  rank: 3,
  center: "Main Center",
  badges: [
    { name: "Perfect Attendance", earned: "2024-01-15", color: "bg-green-100 text-green-800" },
    { name: "Good Student", earned: "2024-01-10", color: "bg-blue-100 text-blue-800" },
    { name: "Helper", earned: "2024-01-05", color: "bg-purple-100 text-purple-800" },
    { name: "<PERSON> Learner", earned: "2023-12-20", color: "bg-yellow-100 text-yellow-800" },
  ],
  recentActivities: [
    { activity: "Completed math homework", xp: 25, date: "2024-01-20" },
    { activity: "Perfect attendance this week", xp: 50, date: "2024-01-19" },
    { activity: "Helped classmate with reading", xp: 30, date: "2024-01-18" },
    { activity: "Scored 95% in science test", xp: 40, date: "2024-01-17" },
  ],
  goals: [
    { goal: "Read 5 books this month", progress: 60, deadline: "2024-01-31" },
    { goal: "Improve handwriting", progress: 80, deadline: "2024-02-15" },
    { goal: "Learn multiplication tables", progress: 45, deadline: "2024-02-28" },
  ],
  weeklyProgress: {
    attendance: 100,
    homework: 85,
    behavior: 95,
    participation: 90,
  },
}

const leaderboard = [
  { rank: 1, name: "Rahul Kumar", xp: 1380, badges: 9 },
  { rank: 2, name: "Anita Singh", xp: 1290, badges: 7 },
  { rank: 3, name: "Priya Sharma", xp: 1250, badges: 8 },
  { rank: 4, name: "Vikram Patel", xp: 1180, badges: 6 },
  { rank: 5, name: "Meera Gupta", xp: 1120, badges: 5 },
]

export default function StudentDashboard() {
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      const parsedUser = JSON.parse(userData)
      if (parsedUser.role !== "student") {
        alert("Access denied. Student role required.")
        window.location.href = "/"
        return
      }
      setUser(parsedUser)
    } else {
      window.location.href = "/"
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem("user")
    window.location.href = "/"
  }

  if (!user) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Dashboard</h1>
              <p className="text-gray-600">Welcome back, {studentData.name}!</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="secondary">Student</Badge>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Hero Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Current Level</p>
                  <p className="text-3xl font-bold">{studentData.level}</p>
                </div>
                <Star className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Total XP</p>
                  <p className="text-3xl font-bold">{studentData.xp}</p>
                </div>
                <Award className="h-8 w-8 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Badges Earned</p>
                  <p className="text-3xl font-bold">{studentData.totalBadges}</p>
                </div>
                <Trophy className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Current Streak</p>
                  <p className="text-3xl font-bold">{studentData.streak}</p>
                </div>
                <Flame className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Level Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Level Progress
            </CardTitle>
            <CardDescription>
              You're {studentData.nextLevelXP - studentData.xp} XP away from Level {studentData.level + 1}!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Level {studentData.level}</span>
                <span>Level {studentData.level + 1}</span>
              </div>
              <Progress value={(studentData.xp / studentData.nextLevelXP) * 100} className="h-3" />
              <p className="text-center text-sm text-gray-600">
                {studentData.xp} / {studentData.nextLevelXP} XP
              </p>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="badges">Badges</TabsTrigger>
            <TabsTrigger value="goals">Goals</TabsTrigger>
            <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activities</CardTitle>
                  <CardDescription>Your latest achievements</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {studentData.recentActivities.map((activity, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{activity.activity}</p>
                          <p className="text-xs text-gray-600">{activity.date}</p>
                        </div>
                        <Badge variant="secondary">+{activity.xp} XP</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Weekly Performance</CardTitle>
                  <CardDescription>How you're doing this week</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Attendance</span>
                      <span className="text-sm text-gray-600">{studentData.weeklyProgress.attendance}%</span>
                    </div>
                    <Progress value={studentData.weeklyProgress.attendance} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Homework</span>
                      <span className="text-sm text-gray-600">{studentData.weeklyProgress.homework}%</span>
                    </div>
                    <Progress value={studentData.weeklyProgress.homework} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Behavior</span>
                      <span className="text-sm text-gray-600">{studentData.weeklyProgress.behavior}%</span>
                    </div>
                    <Progress value={studentData.weeklyProgress.behavior} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Participation</span>
                      <span className="text-sm text-gray-600">{studentData.weeklyProgress.participation}%</span>
                    </div>
                    <Progress value={studentData.weeklyProgress.participation} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="badges">
            <Card>
              <CardHeader>
                <CardTitle>My Badge Collection</CardTitle>
                <CardDescription>Badges you've earned for your achievements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {studentData.badges.map((badge, index) => (
                    <div key={index} className="border rounded-lg p-4 text-center space-y-3">
                      <Award className="h-12 w-12 mx-auto text-yellow-500" />
                      <div
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${badge.color}`}
                      >
                        {badge.name}
                      </div>
                      <p className="text-xs text-gray-600">Earned on {badge.earned}</p>
                    </div>
                  ))}

                  {/* Locked badges */}
                  {[1, 2, 3, 4].map((_, index) => (
                    <div key={index} className="border rounded-lg p-4 text-center space-y-3 opacity-50">
                      <Award className="h-12 w-12 mx-auto text-gray-400" />
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-500">
                        Locked Badge
                      </div>
                      <p className="text-xs text-gray-400">Keep working to unlock!</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="goals">
            <Card>
              <CardHeader>
                <CardTitle>Personal Goals</CardTitle>
                <CardDescription>Track your progress towards your goals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {studentData.goals.map((goal, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{goal.goal}</h3>
                        <Badge variant="outline">
                          <Calendar className="h-3 w-3 mr-1" />
                          {goal.deadline}
                        </Badge>
                      </div>
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Progress</span>
                          <span className="text-sm text-gray-600">{goal.progress}%</span>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="leaderboard">
            <Card>
              <CardHeader>
                <CardTitle>Class Leaderboard</CardTitle>
                <CardDescription>See how you rank among your classmates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {leaderboard.map((student) => (
                    <div
                      key={student.rank}
                      className={`flex items-center justify-between p-4 rounded-lg ${
                        student.name === studentData.name ? "bg-blue-50 border-2 border-blue-200" : "bg-gray-50"
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                            student.rank === 1
                              ? "bg-yellow-100 text-yellow-800"
                              : student.rank === 2
                                ? "bg-gray-100 text-gray-800"
                                : student.rank === 3
                                  ? "bg-orange-100 text-orange-800"
                                  : "bg-blue-100 text-blue-800"
                          }`}
                        >
                          #{student.rank}
                        </div>
                        <div>
                          <p className="font-semibold">{student.name}</p>
                          <p className="text-sm text-gray-600">{student.badges} badges</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{student.xp} XP</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="progress">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Streak</CardTitle>
                  <CardDescription>Your daily activity streak this month</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="inline-flex items-center gap-2 text-2xl font-bold text-orange-600">
                      <Flame className="h-8 w-8" />
                      {studentData.streak} Days
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Keep it up!</p>
                  </div>

                  <div className="grid grid-cols-7 gap-2">
                    {Array.from({ length: 30 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          i < studentData.streak
                            ? "bg-orange-100 text-orange-800 border-2 border-orange-300"
                            : "bg-gray-100 text-gray-400"
                        }`}
                      >
                        {i + 1}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Subject Performance</CardTitle>
                  <CardDescription>Your performance across different subjects</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[
                    { subject: "Mathematics", score: 85, color: "bg-blue-500" },
                    { subject: "Science", score: 92, color: "bg-green-500" },
                    { subject: "English", score: 78, color: "bg-purple-500" },
                    { subject: "Social Studies", score: 88, color: "bg-orange-500" },
                    { subject: "Art", score: 95, color: "bg-pink-500" },
                  ].map((subject, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{subject.subject}</span>
                        <span className="text-sm text-gray-600">{subject.score}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${subject.color}`}
                          style={{ width: `${subject.score}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
