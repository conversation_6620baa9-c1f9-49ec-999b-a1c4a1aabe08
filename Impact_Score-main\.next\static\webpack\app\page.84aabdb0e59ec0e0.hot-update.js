"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        role: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        password: \"\",\n        role: \"\",\n        center: \"\"\n    });\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        // Simple demo authentication\n        if (!loginData.email || !loginData.password || !loginData.role) {\n            alert(\"Please fill in all fields\");\n            return;\n        }\n        // Mock user data\n        const user = {\n            id: \"1\",\n            name: loginData.email.split(\"@\")[0].charAt(0).toUpperCase() + loginData.email.split(\"@\")[0].slice(1),\n            email: loginData.email,\n            role: loginData.role,\n            center: \"Main Center\"\n        };\n        localStorage.setItem(\"user\", JSON.stringify(user));\n        // Redirect based on role\n        const dashboardRoutes = {\n            admin: \"/admin\",\n            teacher: \"/teacher\",\n            student: \"/student\",\n            parent: \"/parent\"\n        };\n        window.location.href = dashboardRoutes[loginData.role] || \"/student\";\n    };\n    const handleRegister = (e)=>{\n        e.preventDefault();\n        // Mock registration\n        alert(\"Registration successful! Please login.\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen gradient-bg-hero\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-10 w-10 text-accent feature-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent\",\n                                    children: \"ImpactScore\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl text-secondary/80 mb-4 font-medium\",\n                            children: \"NGO Student Growth Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-muted-foreground mb-12 max-w-2xl mx-auto\",\n                            children: \"Empowering education through gamified learning experiences and comprehensive progress tracking\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center card-hover glass-effect border-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-8 pb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-12 w-12 text-primary feature-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-lg mb-2 text-secondary\",\n                                                children: \"Multi-Role Access\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                children: \"Seamless access for Admin, Teacher, Student, and Parent roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center card-hover glass-effect border-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-8 pb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-full bg-accent/10 w-fit mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-12 w-12 text-accent feature-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-lg mb-2 text-secondary\",\n                                                children: \"Gamification\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                children: \"Engaging XP Points, Achievement Badges, and Dynamic Leaderboards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center card-hover glass-effect border-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-8 pb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-12 w-12 text-primary feature-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-lg mb-2 text-secondary\",\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                children: \"Comprehensive monitoring of Behavior, Academics, and Attendance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center card-hover glass-effect border-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-8 pb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-full bg-accent/10 w-fit mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-12 w-12 text-accent feature-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-lg mb-2 text-secondary\",\n                                                children: \"Impact Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                children: \"Detailed Reports, Trend Analysis, and Actionable Insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-lg mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass-effect border-0 shadow-2xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"text-center pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-2xl font-bold text-secondary\",\n                                        children: \"Welcome to ImpactScore\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        className: \"text-muted-foreground text-base\",\n                                        children: \"Login or register to begin your educational journey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                    defaultValue: \"login\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                            className: \"grid w-full grid-cols-2 bg-muted/50 p-1 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                    value: \"login\",\n                                                    className: \"data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium\",\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                    value: \"register\",\n                                                    className: \"data-[state=active]:bg-primary data-[state=active]:text-primary-foreground font-medium\",\n                                                    children: \"Register\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                            value: \"login\",\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleLogin,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"email\",\n                                                                    type: \"email\",\n                                                                    placeholder: \"Enter your email\",\n                                                                    value: loginData.email,\n                                                                    onChange: (e)=>setLoginData({\n                                                                            ...loginData,\n                                                                            email: e.target.value\n                                                                        }),\n                                                                    className: \"h-12 border-2 border-border focus:border-primary transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"password\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"password\",\n                                                                    type: \"password\",\n                                                                    placeholder: \"Enter your password\",\n                                                                    value: loginData.password,\n                                                                    onChange: (e)=>setLoginData({\n                                                                            ...loginData,\n                                                                            password: e.target.value\n                                                                        }),\n                                                                    className: \"h-12 border-2 border-border focus:border-primary transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"role\",\n                                                                    className: \"text-secondary font-medium\",\n                                                                    children: \"Select Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                    value: loginData.role,\n                                                                    onValueChange: (value)=>setLoginData({\n                                                                            ...loginData,\n                                                                            role: value\n                                                                        }),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                            className: \"h-12 border-2 border-border focus:border-primary\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                placeholder: \"Choose your role\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 192,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                    value: \"admin\",\n                                                                                    children: \"Admin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 195,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                    value: \"teacher\",\n                                                                                    children: \"Teacher/Volunteer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 196,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                    value: \"student\",\n                                                                                    children: \"Student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 197,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                    value: \"parent\",\n                                                                                    children: \"Parent\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 198,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"submit\",\n                                                            className: \"w-full h-12 btn-primary-gradient text-lg font-semibold\",\n                                                            children: \"Sign In to ImpactScore\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-6 bg-muted/30 rounded-xl border border-border/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold mb-4 text-secondary flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-accent rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Demo Credentials\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-3 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-primary/10 text-primary border-primary/20\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"<EMAIL>\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 216,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-accent/10 text-accent border-accent/20\",\n                                                                            children: \"Teacher\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"<EMAIL>\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-primary/10 text-primary border-primary/20\",\n                                                                            children: \"Student\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"<EMAIL>\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-accent/10 text-accent border-accent/20\",\n                                                                            children: \"Parent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"<EMAIL>\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-3 text-center\",\n                                                            children: [\n                                                                \"Password: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono bg-background px-2 py-1 rounded\",\n                                                                    children: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                            value: \"register\",\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleRegister,\n                                                className: \"space-y-5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"name\",\n                                                                className: \"text-secondary font-medium\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"name\",\n                                                                placeholder: \"Enter your full name\",\n                                                                value: registerData.name,\n                                                                onChange: (e)=>setRegisterData({\n                                                                        ...registerData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"reg-email\",\n                                                                className: \"text-secondary font-medium\",\n                                                                children: \"Email Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"reg-email\",\n                                                                type: \"email\",\n                                                                placeholder: \"Enter your email\",\n                                                                value: registerData.email,\n                                                                onChange: (e)=>setRegisterData({\n                                                                        ...registerData,\n                                                                        email: e.target.value\n                                                                    }),\n                                                                className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"reg-password\",\n                                                                className: \"text-secondary font-medium\",\n                                                                children: \"Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"reg-password\",\n                                                                type: \"password\",\n                                                                placeholder: \"Create a password\",\n                                                                value: registerData.password,\n                                                                onChange: (e)=>setRegisterData({\n                                                                        ...registerData,\n                                                                        password: e.target.value\n                                                                    }),\n                                                                className: \"h-11 border-2 border-border focus:border-primary transition-colors\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"reg-role\",\n                                                                        className: \"text-secondary font-medium\",\n                                                                        children: \"Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                        value: registerData.role,\n                                                                        onValueChange: (value)=>setRegisterData({\n                                                                                ...registerData,\n                                                                                role: value\n                                                                            }),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                className: \"h-11 border-2 border-border focus:border-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                    placeholder: \"Select role\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 285,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"teacher\",\n                                                                                        children: \"Teacher/Volunteer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 288,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"student\",\n                                                                                        children: \"Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 289,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"parent\",\n                                                                                        children: \"Parent\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 290,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"center\",\n                                                                        className: \"text-secondary font-medium\",\n                                                                        children: \"Center\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                        value: registerData.center,\n                                                                        onValueChange: (value)=>setRegisterData({\n                                                                                ...registerData,\n                                                                                center: value\n                                                                            }),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                className: \"h-11 border-2 border-border focus:border-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                    placeholder: \"Select center\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 302,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 301,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"main\",\n                                                                                        children: \"Main Center\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 305,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"north\",\n                                                                                        children: \"North Branch\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 306,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"south\",\n                                                                                        children: \"South Branch\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 307,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                        value: \"east\",\n                                                                                        children: \"East Branch\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full h-12 btn-accent-gradient text-lg font-semibold mt-6\",\n                                                        children: \"Create Account\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"qwi95Ci9fALINql8qdlP71/CyvU=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});