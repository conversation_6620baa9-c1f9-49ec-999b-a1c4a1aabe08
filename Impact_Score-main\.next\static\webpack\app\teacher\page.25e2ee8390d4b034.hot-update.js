"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./app/teacher/page.tsx":
/*!******************************!*\
  !*** ./app/teacher/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,LogOut,Plus,Star,Target,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockStudents = [\n    {\n        id: 1,\n        name: \"Priya Sharma\",\n        xp: 1250,\n        level: 5,\n        attendance: 95,\n        badges: [\n            \"Good Student\",\n            \"Perfect Attendance\",\n            \"Helper\"\n        ],\n        recentActivity: \"Completed math homework\",\n        goals: [\n            \"Improve handwriting\",\n            \"Learn multiplication tables\"\n        ]\n    },\n    {\n        id: 2,\n        name: \"Rahul Kumar\",\n        xp: 980,\n        level: 4,\n        attendance: 88,\n        badges: [\n            \"Team Player\",\n            \"Creative Thinker\"\n        ],\n        recentActivity: \"Helped classmate with reading\",\n        goals: [\n            \"Read 5 books this month\",\n            \"Improve attendance\"\n        ]\n    },\n    {\n        id: 3,\n        name: \"Anita Singh\",\n        xp: 1100,\n        level: 4,\n        attendance: 92,\n        badges: [\n            \"Quick Learner\",\n            \"Good Behavior\"\n        ],\n        recentActivity: \"Scored 90% in science test\",\n        goals: [\n            \"Master fractions\",\n            \"Join art club\"\n        ]\n    }\n];\nconst behaviorCategories = [\n    {\n        id: \"attendance\",\n        name: \"Attendance\",\n        xp: 10\n    },\n    {\n        id: \"homework\",\n        name: \"Homework Completion\",\n        xp: 25\n    },\n    {\n        id: \"behavior\",\n        name: \"Good Behavior\",\n        xp: 15\n    },\n    {\n        id: \"participation\",\n        name: \"Class Participation\",\n        xp: 20\n    },\n    {\n        id: \"hygiene\",\n        name: \"Personal Hygiene\",\n        xp: 10\n    },\n    {\n        id: \"helping\",\n        name: \"Helping Others\",\n        xp: 30\n    }\n];\nfunction TeacherDashboard() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [xpForm, setXpForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"\",\n        points: \"\",\n        note: \"\"\n    });\n    const [goalForm, setGoalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student: \"\",\n        goal: \"\",\n        deadline: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherDashboard.useEffect\": ()=>{\n            const userData = localStorage.getItem(\"user\");\n            if (userData) {\n                const parsedUser = JSON.parse(userData);\n                if (parsedUser.role !== \"teacher\") {\n                    alert(\"Access denied. Teacher role required.\");\n                    window.location.href = \"/\";\n                    return;\n                }\n                setUser(parsedUser);\n            } else {\n                window.location.href = \"/\";\n            }\n        }\n    }[\"TeacherDashboard.useEffect\"], []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"user\");\n        window.location.href = \"/\";\n    };\n    const handleAwardXP = (e)=>{\n        e.preventDefault();\n        alert(\"Awarded \".concat(xpForm.points, \" XP to student for \").concat(xpForm.category));\n        setXpForm({\n            category: \"\",\n            points: \"\",\n            note: \"\"\n        });\n    };\n    const handleSetGoal = (e)=>{\n        e.preventDefault();\n        alert(\"Goal set for student: \".concat(goalForm.goal));\n        setGoalForm({\n            student: \"\",\n            goal: \"\",\n            deadline: \"\"\n        });\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen gradient-bg-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-effect border-b border-border/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-secondary\",\n                                                children: \"Teacher Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-lg\",\n                                                children: [\n                                                    \"Welcome back, \",\n                                                    user.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        className: \"bg-primary/10 text-primary border-primary/20 px-4 py-2 text-sm font-medium\",\n                                        children: \"Teacher\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleLogout,\n                                        className: \"border-2 hover:bg-destructive hover:text-destructive-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Logout\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"My Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: mockStudents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"XP Awarded Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"450\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-accent/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-8 w-8 text-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"Avg Attendance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"92%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-8 w-8 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"card-hover glass-effect border-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-muted-foreground mb-1\",\n                                                        children: \"Active Goals\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-secondary\",\n                                                        children: \"12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-full bg-accent/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-8 w-8 text-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                        defaultValue: \"students\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"students\",\n                                        children: \"My Students\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"evaluate\",\n                                        children: \"Behavior Evaluation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"goals\",\n                                        children: \"Set Goals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                        value: \"badges\",\n                                        children: \"Award Badges\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"students\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                                    children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-lg\",\n                                                                    children: student.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        \"Level \",\n                                                                        student.level\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: [\n                                                                student.xp,\n                                                                \" XP • \",\n                                                                student.attendance,\n                                                                \"% attendance\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"Progress to Next Level\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                student.xp,\n                                                                                \"/1500 XP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 216,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_10__.Progress, {\n                                                                    value: student.xp / 1500 * 100,\n                                                                    className: \"h-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: \"Recent Activity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: student.recentActivity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: [\n                                                                        \"Badges (\",\n                                                                        student.badges.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: student.badges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: badge\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium mb-2\",\n                                                                    children: \"Current Goals\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"text-sm text-gray-600 space-y-1\",\n                                                                    children: student.goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                goal\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, student.id, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"evaluate\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            children: \"Award XP Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: \"Recognize student achievements and behaviors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: handleAwardXP,\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"student-select\",\n                                                                        children: \"Select Student\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                        value: selectedStudent,\n                                                                        onValueChange: setSelectedStudent,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                    placeholder: \"Choose a student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 267,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                        value: student.id.toString(),\n                                                                                        children: student.name\n                                                                                    }, student.id, false, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                        lineNumber: 271,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 269,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"category\",\n                                                                        children: \"Behavior Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                        value: xpForm.category,\n                                                                        onValueChange: (value)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                category: value\n                                                                            }),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                    placeholder: \"Select category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                    lineNumber: 286,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 285,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                children: behaviorCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                        value: category.id,\n                                                                                        children: [\n                                                                                            category.name,\n                                                                                            \" (+\",\n                                                                                            category.xp,\n                                                                                            \" XP)\"\n                                                                                        ]\n                                                                                    }, category.id, true, {\n                                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                        lineNumber: 290,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"points\",\n                                                                        children: \"XP Points\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"points\",\n                                                                        type: \"number\",\n                                                                        value: xpForm.points,\n                                                                        onChange: (e)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                points: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter XP amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"note\",\n                                                                        children: \"Note (Optional)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                        id: \"note\",\n                                                                        value: xpForm.note,\n                                                                        onChange: (e)=>setXpForm({\n                                                                                ...xpForm,\n                                                                                note: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Add a motivational note...\",\n                                                                        rows: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"submit\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Award XP\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            children: \"Behavior Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: \"Quick reference for XP values\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: behaviorCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: category.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            category.xp,\n                                                                            \" XP\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, category.id, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"goals\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Set Personal Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Create personalized goals for students\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSetGoal,\n                                                className: \"space-y-4 max-w-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"goal-student\",\n                                                                children: \"Select Student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                value: goalForm.student,\n                                                                onValueChange: (value)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        student: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                            placeholder: \"Choose a student\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                        children: mockStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                value: student.id.toString(),\n                                                                                children: student.name\n                                                                            }, student.id, false, {\n                                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"goal-text\",\n                                                                children: \"Goal Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                id: \"goal-text\",\n                                                                value: goalForm.goal,\n                                                                onChange: (e)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        goal: e.target.value\n                                                                    }),\n                                                                placeholder: \"Describe the goal...\",\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"deadline\",\n                                                                children: \"Target Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"deadline\",\n                                                                type: \"date\",\n                                                                value: goalForm.deadline,\n                                                                onChange: (e)=>setGoalForm({\n                                                                        ...goalForm,\n                                                                        deadline: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Set Goal\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"badges\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Award Special Badges\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Recognize exceptional achievements\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    {\n                                                        name: \"Perfect Attendance\",\n                                                        description: \"100% attendance for a month\",\n                                                        color: \"bg-green-100 text-green-800\"\n                                                    },\n                                                    {\n                                                        name: \"Academic Excellence\",\n                                                        description: \"Top performer in academics\",\n                                                        color: \"bg-blue-100 text-blue-800\"\n                                                    },\n                                                    {\n                                                        name: \"Team Leader\",\n                                                        description: \"Outstanding leadership skills\",\n                                                        color: \"bg-purple-100 text-purple-800\"\n                                                    },\n                                                    {\n                                                        name: \"Creative Genius\",\n                                                        description: \"Exceptional creativity\",\n                                                        color: \"bg-pink-100 text-pink-800\"\n                                                    },\n                                                    {\n                                                        name: \"Helper\",\n                                                        description: \"Always helps classmates\",\n                                                        color: \"bg-yellow-100 text-yellow-800\"\n                                                    },\n                                                    {\n                                                        name: \"Improvement Star\",\n                                                        description: \"Most improved student\",\n                                                        color: \"bg-orange-100 text-orange-800\"\n                                                    }\n                                                ].map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4 space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium \".concat(badge.color),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_LogOut_Plus_Star_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    badge.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: badge.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"w-full bg-transparent\",\n                                                                children: \"Award Badge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Github\\\\Impact_Score-main\\\\Impact_Score-main\\\\app\\\\teacher\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherDashboard, \"gdBBXXB8Wm7SgUU6y6zzRr89uIY=\");\n_c = TeacherDashboard;\nvar _c;\n$RefreshReg$(_c, \"TeacherDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/teacher/page.tsx\n"));

/***/ })

});