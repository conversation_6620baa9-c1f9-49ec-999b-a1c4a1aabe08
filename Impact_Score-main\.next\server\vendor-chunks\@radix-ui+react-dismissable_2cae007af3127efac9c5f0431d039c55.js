"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dismissable_2cae007af3127efac9c5f0431d039c55";
exports.ids = ["vendor-chunks/@radix-ui+react-dismissable_2cae007af3127efac9c5f0431d039c55"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_2cae007af3127efac9c5f0431d039c55/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dismissable_2cae007af3127efac9c5f0431d039c55/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_8f0d512ccc09326835f5f2cdc5adfada/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_25dcd186884475f3251d0d7736e0c509/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_38edf6de4f20531ee90c0bca97740811/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-escape-_c00d463fae91c75103e5ab37dde1fa07/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_2cae007af3127efac9c5f0431d039c55/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ })

};
;